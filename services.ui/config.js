// Configuration for the Voice Gateway Client
const CONFIG = {
    // Supabase configuration
    // NOTE: Updated to use new Supabase API key format (August 2025)
    // Legacy anon/service_role keys were disabled. Use new publishable/secret keys.
    // Get your new keys from: https://supabase.com/dashboard/project/YOUR_PROJECT/settings/api
    supabase: {
        url: 'https://jkkxvdaponymlwtmzdrn.supabase.co',
        publishableKey: 'sb_publishable_F8ACvXQ0NVuHpsYxZNRlCw_V1fxIvhD' // Replace with your actual Supabase publishable key (sb_publishable_...)
    },
    
    // Gateway endpoints
    gateway: {
        baseUrl: 'http://localhost',
        voiceGatewayPath: '/api/v1/voice-gateway',
        websocketUrl: 'ws://localhost',  // Use port 80 (Traefik)
        websocketPath: '/api/v1/call/ws/call'  // Match actual FastAPI route
    },
    
    // Audio settings
    audio: {
        sampleRate: 16000,
        bufferSize: 1024,
        channelCount: 1
    },
    
    // Default settings
    defaults: {
        targetLanguage: 'es'
    }
};

// Initialize Supabase client
const supabase = window.supabase.createClient(CONFIG.supabase.url, CONFIG.supabase.publishableKey);
