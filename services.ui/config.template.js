// Configuration template for the Voice Gateway Client
// Copy this file to config.js and update with your actual values

const CONFIG = {
    // Supabase configuration
    // NOTE: Updated to use new Supabase API key format (August 2025)
    // Legacy anon/service_role keys were disabled. Use new publishable/secret keys.
    // Get your new keys from: https://supabase.com/dashboard/project/YOUR_PROJECT/settings/api
    supabase: {
        url: 'https://your-project-id.supabase.co',
        publishableKey: 'your-supabase-publishable-key-here' // Use the new publishable key (sb_publishable_...)
    },
    
    // Gateway endpoints
    // Update these if your gateway is running on different URLs/ports
    gateway: {
        baseUrl: 'http://localhost',
        voiceGatewayPath: '/api/v1/voice-gateway',
        websocketUrl: 'ws://localhost',
        websocketPath: '/api/v1/voice-gateway/call/ws/call'
    },
    
    // Audio settings
    // These should match your voice gateway service configuration
    audio: {
        sampleRate: 16000,      // Target sample rate for voice processing
        bufferSize: 1024,       // Audio buffer size (power of 2: 256, 512, 1024, 2048, 4096)
        channelCount: 1         // Mono audio
    },
    
    // Default settings
    defaults: {
        targetLanguage: 'es'    // Default target language for translation
    }
};

// Initialize Supabase client
const supabase = window.supabase.createClient(CONFIG.supabase.url, CONFIG.supabase.publishableKey);
